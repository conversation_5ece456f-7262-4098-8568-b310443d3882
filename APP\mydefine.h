#ifndef __MYDEFINE_H_
#define __MYDEFINE_H_

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

#include "main.h"
#include "usart.h"
#include "gpio.h"

#include "uart.h"
#include "Emm_V5.h"
#include "scheduler.h"
#include "usart_app.h"
#include "interrupt.h"
#include "pid.h"
#include "app_motor.h"
#include "app_Point2D.h"
#include "flash_storage.h"
#include "Relay.h"

extern int num1;
extern int num2;

#endif
