#include "usart_app.h"
#include "mydefine.h"

uint64_t uart_rx_ticks;
unsigned char uart_rx_index;
unsigned char uart_rx_buffer[128] = {0};
unsigned char uart_flag;

uint64_t uart_motor_y_rx_ticks;
unsigned char uart_motor_y_rx_index;
unsigned char uart_motor_y_rx_buffer[128] = {0};
unsigned char uart_motor_y_flag;

uint64_t uart_motor_x_rx_ticks;
unsigned char uart_motor_x_rx_index;
unsigned char uart_motor_x_rx_buffer[128] = {0};
unsigned char uart_motor_x_flag;


uint64_t uart_camera_rx_ticks;
unsigned char uart_camera_rx_index;
unsigned char uart_camera_rx_buffer[128] = {0};
unsigned char uart_camera_flag;

// FF命令数据记录数组
unsigned char ff_data_array[4] = {0};  // 四位数组
unsigned char ff_array_index = 0;      // 当前索引位置
int32_t ff_pos_x_array[4] = {0};       // X位置数组
int32_t ff_pos_y_array[4] = {0};       // Y位置数组

int32_t pos_y;
int32_t pos_x;

int32_t center_pos_x;
int32_t center_pos_y;

uint32_t current_x;
uint32_t current_y;

uint8_t step_y_flag;
uint8_t step_x_flag;

int16_t camera_x_error = 0;  // 摄像头X轴误差值（带符号，范围-255到255）
int16_t camera_y_error = 0;  // 摄像头Y轴误差值（带符号，范围-255到255）
	
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;

	va_start(arg, format);

	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	HAL_UART_Transmit(huart, (unsigned char *)buffer, (unsigned short)len, 0xFF);
	return len;
}

// 初始化串口接收
void uart_init(void)
{
    // 启动UART1中断接收 (上位机命令)
    HAL_UART_Receive_IT(&huart1, uart_rx_buffer, 1);

	HAL_UART_Receive_IT(&huart2, uart_motor_y_rx_buffer, 1);
	HAL_UART_Receive_IT(&huart3, uart_motor_x_rx_buffer, 1);
	HAL_UART_Receive_IT(&huart6, uart_camera_rx_buffer, 1);
}


void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if (huart->Instance == USART1)
	{
		uart_rx_ticks = HAL_GetTick();
		uart_rx_index++;

		HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);
	}
	if (huart->Instance == USART2)
	{
		uart_motor_y_rx_ticks = HAL_GetTick();
		uart_motor_y_rx_index++;

		HAL_UART_Receive_IT(&huart2, &uart_motor_y_rx_buffer[uart_motor_y_rx_index], 1);
	}
	if (huart->Instance == USART3)
	{
		uart_motor_x_rx_ticks = HAL_GetTick();
		uart_motor_x_rx_index++;

		HAL_UART_Receive_IT(&huart3, &uart_motor_x_rx_buffer[uart_motor_x_rx_index], 1);
	}
	if (huart->Instance == USART6)
	{
		uart_camera_rx_ticks = HAL_GetTick();
		uart_camera_rx_index++;

		HAL_UART_Receive_IT(&huart6, &uart_camera_rx_buffer[uart_camera_rx_index], 1);
	}
}

void uart1_process()
{
    // 检查是否接收到完整的3字节数据包：0x66 + 数据 + 0x77
    if (uart_rx_index >= 3)
    {
        // 查找帧头0x66
        for (int i = 0; i <= uart_rx_index - 3; i++)
        {
            if (uart_rx_buffer[i] == 0x66 && uart_rx_buffer[i + 2] == 0x77)
            {
                // 找到完整数据包：帧头0x66 + 数据 + 帧尾0x77
                unsigned char cmd_data = uart_rx_buffer[i + 1];

                // 根据接收到的命令执行相应操作
                switch (cmd_data)
                {
                    case 0x01:  // 命令01
                        jiaozhun_01();
                        break;
                    case 0x02:  // 命令02
                        jiaozhun_02();
                        break;
                    case 0x03:  // 命令03
                        jiaozhun_03();
                        break;
                    case 0x04:  // 命令04
                        jiaozhun_04();
                        break;
                    case 0xFF:  // 命令FF
                        handle_cmd_ff();
                        break;
                    default:
                        // 未知命令，不处理
                        break;
                }
                break;
            }
            // 查找帧头0x88
            if (uart_rx_buffer[i] == 0x88 && uart_rx_buffer[i + 2] == 0x99)
            {
                // 找到完整数据包：帧头0x88 + 数据 + 帧尾0x99
                unsigned char cmd_data = uart_rx_buffer[i + 1];

                // 根据接收到的命令执行相应操作
                switch (cmd_data)
                {
                    case 0x01:  // 88命令01
                        task_01();
                        break;
                    case 0x02:  // 88命令02
                        task_02();
                        break;
                    case 0x03:  // 88命令03
                        task_03();
                        break;
                    case 0x04:  // 88命令04
                        task_04();
                        break;
                    default:
                        // 未知命令，不处理
                        break;
                }
                break; // 找到一个有效包后退出循环
            }
        }
    }
}

void uart2_process()
{
    // 检查是否接收到完整的5字节数据包：0x44 + 符号位 + X误差 + 符号位 + Y误差 + 0x55
    if (uart_camera_rx_index >= 6)
    {
        // 查找帧头0x44
        for (int i = 0; i <= uart_camera_rx_index - 6; i++)
        {
            if (uart_camera_rx_buffer[i] == 0x44 && uart_camera_rx_buffer[i + 5] == 0x55)
            {
                uint8_t x_sign = uart_camera_rx_buffer[i + 1];  // X轴符号位
                uint8_t x_value = uart_camera_rx_buffer[i + 2]; // X轴误差值
                uint8_t y_sign = uart_camera_rx_buffer[i + 3];  // Y轴符号位
                uint8_t y_value = uart_camera_rx_buffer[i + 4]; // Y轴误差值

                // 处理X轴误差值（带符号）
                if (x_sign == 0x00) {
                    camera_x_error = (int16_t)x_value;  // 正值
                } else if (x_sign == 0x01) {
                    camera_x_error = -(int16_t)x_value; // 负值
                }

                // 处理Y轴误差值（带符号）
                if (y_sign == 0x00) {
                    camera_y_error = (int16_t)y_value;  // 正值
                } else if (y_sign == 0x01) {
                    camera_y_error = -(int16_t)y_value; // 负值
                }
				//my_printf(&huart1,"%d,%d\r\n",camera_x_error,camera_y_error);
                break; // 找到一个有效包后退出循环
            }
        }
    }
}
void uart_task(void)
{
	if ((HAL_GetTick() - uart_rx_ticks > 10) && uart_rx_index != 0)  // 超时10ms
	{
		uart1_process();
		memset(uart_rx_buffer, 0, uart_rx_index);
		uart_rx_index = 0;
		huart1.pRxBuffPtr = uart_rx_buffer;
	}
	if ((HAL_GetTick() - uart_camera_rx_ticks > 10) && uart_camera_rx_index != 0)  // 超时10ms
	{
		uart2_process();
		memset(uart_camera_rx_buffer, 0, uart_camera_rx_index);
		uart_camera_rx_index = 0;
		huart6.pRxBuffPtr = uart_camera_rx_buffer;
	}
	if ((HAL_GetTick() - uart_motor_y_rx_ticks > 10) && uart_motor_y_rx_index != 0)  // 超时10ms
	{
		for(int i = 0; i < 100; i++)
		{
			if(uart_motor_y_rx_buffer[i] == 1 && uart_motor_y_rx_buffer[i + 1] == 0x32 && uart_motor_y_rx_buffer[i+7] == 0x6B)//读取脉冲
			{
				// 拼接成uint32_t类型
				pos_y = (uint32_t)(
								((uint32_t)uart_motor_y_rx_buffer[i + 3] << 24)    |
								((uint32_t)uart_motor_y_rx_buffer[i + 4] << 16)    |
								((uint32_t)uart_motor_y_rx_buffer[i + 5] << 8)     |
								((uint32_t)uart_motor_y_rx_buffer[i + 6] << 0)
								);
				if(uart_motor_y_rx_buffer[2]) { pos_y = -pos_y;}
				i += 8;
			}
			if(uart_motor_y_rx_buffer[i] == 1 && uart_motor_y_rx_buffer[i + 1] == 0x3a && uart_motor_y_rx_buffer[i + 3] == 0x6B)//状态
			{
				step_y_flag = 0x02 & uart_motor_y_rx_buffer[i + 2];
				i += 4;
			}
		}
		memset(uart_motor_y_rx_buffer, 0, uart_motor_y_rx_index);
		uart_motor_y_rx_index = 0;
		huart2.pRxBuffPtr = uart_motor_y_rx_buffer;
	}
	if ((HAL_GetTick() - uart_motor_x_rx_ticks > 10) && uart_motor_x_rx_index != 0)  // 超时10ms
	{
		for(int i = 0; i < 100; i++)
		{
			if(uart_motor_x_rx_buffer[i] == 1 && uart_motor_x_rx_buffer[i + 1] == 0x32 && uart_motor_x_rx_buffer[i+7] == 0x6B)//读取脉冲
			{
				// 拼接成uint32_t类型
				pos_x = (uint32_t)(
								((uint32_t)uart_motor_x_rx_buffer[i + 3] << 24)    |
								((uint32_t)uart_motor_x_rx_buffer[i + 4] << 16)    |
								((uint32_t)uart_motor_x_rx_buffer[i + 5] << 8)     |
								((uint32_t)uart_motor_x_rx_buffer[i + 6] << 0)
								);
				if(uart_motor_x_rx_buffer[2]) { pos_x = -pos_x;}
				i += 8;
			}
			if(uart_motor_x_rx_buffer[i] == 1 && uart_motor_x_rx_buffer[i + 1] == 0x3a && uart_motor_x_rx_buffer[i + 3] == 0x6B)//状态
			{
				step_x_flag = 0x02 & uart_motor_x_rx_buffer[i + 2];
				i += 4;
			}
		}
		memset(uart_motor_x_rx_buffer, 0, uart_motor_x_rx_index);
		uart_motor_x_rx_index = 0;
		huart3.pRxBuffPtr = uart_motor_x_rx_buffer;
	}
}

void jiaozhun_01(void)
{
	current_y -= 20;
    motorB_pos_control(current_y,200);
}

void jiaozhun_02(void)
{
	current_y += 20;
    motorB_pos_control(current_y,200);
}

void jiaozhun_03(void)
{
	current_x -= 20;
    motorA_pos_control(current_x,200);
}

void jiaozhun_04(void)
{
	current_x += 20;
    motorA_pos_control(current_x,200);
}
void handle_cmd_ff(void) //校准写入flash
{
   	FlashStorage_StoreData1(pos_x);
	FlashStorage_StoreData2(pos_y);
}
void task_01(void)//第一问
{
	
}

void task_02(void)//第二问
{
	//重置状态
	State_Machine.STATE_TASK2 = task2_state0;
    State_Machine.MAIN_STATE = TASK_2; //主状态机到任务2
}

void task_03(void) //第三问
{
	//重置状态
	State_Machine.STATE_TASK3= task3_state0;
	//重置状态
    State_Machine.MAIN_STATE = TASK_3; //主状态机到任务3
}

void task_04(void)
{
    // 88命令04处理逻辑
}	


