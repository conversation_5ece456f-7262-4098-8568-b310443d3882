/**
 * @file flash_storage_improved.c
 * @brief 改进的STM32F407VET6 Flash存储管理实现
 * <AUTHOR> (米醋电子工作室) - 基于网络调研优化
 * @version 2.0
 * @date 2025-01-30
 * @note 解决HAL库兼容性问题，增加寄存器直接操作支持
 */

/* Includes ------------------------------------------------------------------*/
#include "flash_storage.h"
#include <stdio.h>

/* Private typedef -----------------------------------------------------------*/
typedef enum {
    FLASH_METHOD_HAL = 0,      // 使用HAL库
    FLASH_METHOD_REGISTER      // 使用寄存器直接操作
} FlashMethod_t;

/* Private define ------------------------------------------------------------*/
#define FLASH_TIMEOUT_VALUE     50000UL
#define FLASH_SECTOR_6_NUMBER   6
#define FLASH_SECTOR_7_NUMBER   7

/* Private variables ---------------------------------------------------------*/
static FlashMethod_t current_method = FLASH_METHOD_HAL;  // 默认使用HAL库

/* Private function prototypes -----------------------------------------------*/
static uint32_t Flash_ReadWord(uint32_t address);
static FlashStorage_Status Flash_DiagnoseAndClear(void);
static FlashStorage_Status Flash_EraseSector_HAL(uint32_t sector);
static FlashStorage_Status Flash_EraseSector_Register(uint32_t sector_number);
static FlashStorage_Status Flash_ProgramWord_HAL(uint32_t address, uint32_t data);
static FlashStorage_Status Flash_ProgramWord_Register(uint32_t address, uint32_t data);
static bool Flash_VerifyErase(uint32_t start_addr, uint32_t size);
static bool Flash_VerifyWrite(uint32_t address, uint32_t expected_data);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 从Flash读取一个字(32位)
 * @param address: 读取地址
 * @retval uint32_t 读取的数据
 */
static uint32_t Flash_ReadWord(uint32_t address)
{
    return (*(__IO uint32_t*)address);
}

/**
 * @brief 诊断Flash状态并清除错误标志
 * @retval FlashStorage_Status 操作状态
 */
static FlashStorage_Status Flash_DiagnoseAndClear(void)
{
    uint32_t flash_sr = FLASH->SR;
    
    // 打印诊断信息
    printf("Flash SR Register: 0x%08X\r\n", flash_sr);
    printf("Flash CR Register: 0x%08X\r\n", FLASH->CR);
    
    // 检查具体错误
    if(flash_sr & FLASH_SR_WRPERR) {
        printf("ERROR: Write Protection Error\r\n");
        return FLASH_STORAGE_WRITE_PROTECTED;
    }
    if(flash_sr & FLASH_SR_PGAERR) {
        printf("ERROR: Programming Alignment Error\r\n");
        return FLASH_STORAGE_ALIGNMENT_ERROR;
    }
    if(flash_sr & FLASH_SR_PGPERR) {
        printf("ERROR: Programming Parallelism Error\r\n");
        return FLASH_STORAGE_PARALLELISM_ERROR;
    }
    if(flash_sr & FLASH_SR_PGSERR) {
        printf("ERROR: Programming Sequence Error\r\n");
        return FLASH_STORAGE_SEQUENCE_ERROR;
    }
    if(flash_sr & FLASH_SR_OPERR) {
        printf("ERROR: Operation Error\r\n");
        return FLASH_STORAGE_OPERATION_ERROR;
    }
    
    // 清除所有错误标志
    FLASH->SR = FLASH_SR_EOP | FLASH_SR_OPERR | FLASH_SR_WRPERR | 
                FLASH_SR_PGAERR | FLASH_SR_PGPERR | FLASH_SR_PGSERR;
    
    printf("Flash status OK, flags cleared\r\n");
    return FLASH_STORAGE_OK;
}

/**
 * @brief 使用HAL库擦除扇区
 * @param sector: 扇区号
 * @retval FlashStorage_Status 操作状态
 */
static FlashStorage_Status Flash_EraseSector_HAL(uint32_t sector)
{
    HAL_StatusTypeDef hal_status;
    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t SectorError = 0;
    
    // 先诊断并清除错误标志
    FlashStorage_Status diag_status = Flash_DiagnoseAndClear();
    if(diag_status != FLASH_STORAGE_OK) {
        return diag_status;
    }
    
    // 配置擦除参数
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_SECTORS;
    EraseInitStruct.VoltageRange = FLASH_VOLTAGE_RANGE_3;
    EraseInitStruct.Sector = sector;
    EraseInitStruct.NbSectors = 1;
    
    // 执行擦除
    hal_status = HAL_FLASHEx_Erase(&EraseInitStruct, &SectorError);
    if(hal_status != HAL_OK) {
        printf("HAL Erase failed: %d, SectorError: %d\r\n", hal_status, (int)SectorError);
        return FLASH_STORAGE_ERROR;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 使用寄存器直接操作擦除扇区
 * @param sector_number: 扇区号
 * @retval FlashStorage_Status 操作状态
 */
static FlashStorage_Status Flash_EraseSector_Register(uint32_t sector_number)
{
    uint32_t timeout = FLASH_TIMEOUT_VALUE;
    
    // 等待Flash操作完成
    while((FLASH->SR & FLASH_SR_BSY) && timeout--) {
        if(timeout == 0) return FLASH_STORAGE_TIMEOUT;
    }
    
    // 清除错误标志
    FLASH->SR = FLASH_SR_EOP | FLASH_SR_OPERR | FLASH_SR_WRPERR | 
                FLASH_SR_PGAERR | FLASH_SR_PGPERR | FLASH_SR_PGSERR;
    
    // 设置扇区擦除
    FLASH->CR &= ~FLASH_CR_SNB;  // 清除扇区号
    FLASH->CR |= FLASH_CR_SER | (sector_number << FLASH_CR_SNB_Pos);  // 设置扇区擦除和扇区号
    FLASH->CR |= FLASH_CR_STRT;  // 开始擦除
    
    // 等待操作完成
    timeout = FLASH_TIMEOUT_VALUE;
    while((FLASH->SR & FLASH_SR_BSY) && timeout--) {
        if(timeout == 0) return FLASH_STORAGE_TIMEOUT;
    }
    
    // 清除擦除标志
    FLASH->CR &= ~(FLASH_CR_SER | FLASH_CR_SNB);
    
    // 检查错误
    if(FLASH->SR & (FLASH_SR_OPERR | FLASH_SR_WRPERR | FLASH_SR_PGAERR | 
                    FLASH_SR_PGPERR | FLASH_SR_PGSERR)) {
        printf("Register erase failed, SR: 0x%08X\r\n", FLASH->SR);
        return FLASH_STORAGE_ERROR;
    }
    
    printf("Register erase completed successfully\r\n");
    return FLASH_STORAGE_OK;
}

/**
 * @brief 使用HAL库写入字
 * @param address: 写入地址
 * @param data: 写入数据
 * @retval FlashStorage_Status 操作状态
 */
static FlashStorage_Status Flash_ProgramWord_HAL(uint32_t address, uint32_t data)
{
    HAL_StatusTypeDef hal_status;
    
    // 先诊断并清除错误标志
    FlashStorage_Status diag_status = Flash_DiagnoseAndClear();
    if(diag_status != FLASH_STORAGE_OK) {
        return diag_status;
    }
    
    // 写入数据
    hal_status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, address, data);
    if(hal_status != HAL_OK) {
        printf("HAL Program failed: %d\r\n", hal_status);
        Flash_DiagnoseAndClear();  // 再次诊断错误
        return FLASH_STORAGE_ERROR;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 使用寄存器直接操作写入字
 * @param address: 写入地址
 * @param data: 写入数据
 * @retval FlashStorage_Status 操作状态
 */
static FlashStorage_Status Flash_ProgramWord_Register(uint32_t address, uint32_t data)
{
    uint32_t timeout = FLASH_TIMEOUT_VALUE;
    
    // 等待Flash操作完成
    while((FLASH->SR & FLASH_SR_BSY) && timeout--) {
        if(timeout == 0) return FLASH_STORAGE_TIMEOUT;
    }
    
    // 清除错误标志
    FLASH->SR = FLASH_SR_EOP | FLASH_SR_OPERR | FLASH_SR_WRPERR | 
                FLASH_SR_PGAERR | FLASH_SR_PGPERR | FLASH_SR_PGSERR;
    
    // 设置编程模式
    FLASH->CR &= ~FLASH_CR_PSIZE;
    FLASH->CR |= FLASH_CR_PG | FLASH_CR_PSIZE_1;  // 32位编程
    
    // 写入数据
    *(__IO uint32_t*)address = data;
    
    // 等待操作完成
    timeout = FLASH_TIMEOUT_VALUE;
    while((FLASH->SR & FLASH_SR_BSY) && timeout--) {
        if(timeout == 0) return FLASH_STORAGE_TIMEOUT;
    }
    
    // 清除编程标志
    FLASH->CR &= ~(FLASH_CR_PG | FLASH_CR_PSIZE);
    
    // 检查错误
    if(FLASH->SR & (FLASH_SR_OPERR | FLASH_SR_WRPERR | FLASH_SR_PGAERR | 
                    FLASH_SR_PGPERR | FLASH_SR_PGSERR)) {
        printf("Register program failed, SR: 0x%08X\r\n", FLASH->SR);
        return FLASH_STORAGE_ERROR;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 验证扇区擦除效果
 * @param start_addr: 起始地址
 * @param size: 检查大小(字节)
 * @retval bool true=擦除成功, false=擦除失败
 */
static bool Flash_VerifyErase(uint32_t start_addr, uint32_t size)
{
    for(uint32_t i = 0; i < size; i += 4) {
        uint32_t data = *(uint32_t*)(start_addr + i);
        if(data != 0xFFFFFFFF) {
            printf("Erase verification failed at 0x%08X: 0x%08X\r\n", 
                   start_addr + i, data);
            return false;
        }
    }
    printf("Erase verification passed for %d bytes\r\n", (int)size);
    return true;
}

/**
 * @brief 验证写入效果
 * @param address: 写入地址
 * @param expected_data: 期望数据
 * @retval bool true=写入成功, false=写入失败
 */
static bool Flash_VerifyWrite(uint32_t address, uint32_t expected_data)
{
    uint32_t actual_data = Flash_ReadWord(address);
    if(actual_data != expected_data) {
        printf("Write verification failed at 0x%08X: expected 0x%08X, got 0x%08X\r\n", 
               address, expected_data, actual_data);
        return false;
    }
    printf("Write verification passed at 0x%08X: 0x%08X\r\n", address, actual_data);
    return true;
}

/* Exported functions --------------------------------------------------------*/

/**
 * @brief 初始化Flash存储模块
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_Init(void)
{
    // 检查Flash是否已解锁
    if(FLASH->CR & FLASH_CR_LOCK) {
        HAL_FLASH_Unlock();
        HAL_FLASH_Lock();
    }
    
    // 诊断当前Flash状态
    FlashStorage_Status status = Flash_DiagnoseAndClear();
    if(status != FLASH_STORAGE_OK) {
        printf("Flash initialization failed, switching to register method\r\n");
        current_method = FLASH_METHOD_REGISTER;
    } else {
        printf("Flash initialization successful, using HAL method\r\n");
        current_method = FLASH_METHOD_HAL;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 设置Flash操作方法
 * @param method: 操作方法 (0=HAL库, 1=寄存器)
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_SetMethod(uint8_t method)
{
    if(method > 1) {
        return FLASH_STORAGE_INVALID_PARAM;
    }
    
    current_method = (FlashMethod_t)method;
    printf("Flash method set to: %s\r\n", 
           (method == FLASH_METHOD_HAL) ? "HAL Library" : "Register Direct");
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 改进的写入第一个数据的信息
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData1_Improved(uint8_t sign, uint32_t size)
{
    FlashStorage_Status status;
    
    // 参数检查
    if(!IS_VALID_SIGN(sign) || !IS_VALID_SIZE(size)) {
        return FLASH_STORAGE_INVALID_PARAM;
    }
    
    printf("Starting improved write to Data1: sign=%d, size=%d\r\n", sign, size);
    
    // 解锁Flash
    HAL_FLASH_Unlock();
    
    // 根据当前方法选择擦除方式
    if(current_method == FLASH_METHOD_HAL) {
        status = Flash_EraseSector_HAL(FLASH_SECTOR_6);
    } else {
        status = Flash_EraseSector_Register(FLASH_SECTOR_6_NUMBER);
    }
    
    if(status != FLASH_STORAGE_OK) {
        HAL_FLASH_Lock();
        printf("Sector 6 erase failed, trying register method\r\n");
        
        // 如果HAL方法失败，尝试寄存器方法
        if(current_method == FLASH_METHOD_HAL) {
            current_method = FLASH_METHOD_REGISTER;
            status = Flash_EraseSector_Register(FLASH_SECTOR_6_NUMBER);
            if(status != FLASH_STORAGE_OK) {
                HAL_FLASH_Lock();
                return status;
            }
        } else {
            HAL_FLASH_Lock();
            return status;
        }
    }
    
    // 验证擦除效果
    if(!Flash_VerifyErase(DATA1_SIGN_ADDR, 8)) {
        HAL_FLASH_Lock();
        return FLASH_STORAGE_ERASE_VERIFY_FAILED;
    }
    
    // 写入符号位
    if(current_method == FLASH_METHOD_HAL) {
        status = Flash_ProgramWord_HAL(DATA1_SIGN_ADDR, (uint32_t)sign);
    } else {
        status = Flash_ProgramWord_Register(DATA1_SIGN_ADDR, (uint32_t)sign);
    }
    
    if(status != FLASH_STORAGE_OK) {
        HAL_FLASH_Lock();
        return status;
    }
    
    // 验证符号位写入
    if(!Flash_VerifyWrite(DATA1_SIGN_ADDR, (uint32_t)sign)) {
        HAL_FLASH_Lock();
        return FLASH_STORAGE_WRITE_VERIFY_FAILED;
    }
    
    // 写入大小
    if(current_method == FLASH_METHOD_HAL) {
        status = Flash_ProgramWord_HAL(DATA1_SIZE_ADDR, size);
    } else {
        status = Flash_ProgramWord_Register(DATA1_SIZE_ADDR, size);
    }
    
    if(status != FLASH_STORAGE_OK) {
        HAL_FLASH_Lock();
        return status;
    }
    
    // 验证大小写入
    if(!Flash_VerifyWrite(DATA1_SIZE_ADDR, size)) {
        HAL_FLASH_Lock();
        return FLASH_STORAGE_WRITE_VERIFY_FAILED;
    }
    
    // 锁定Flash
    HAL_FLASH_Lock();
    
    printf("Data1 write completed successfully\r\n");
    return FLASH_STORAGE_OK;
}

/**
 * @brief Flash稳定性测试
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_StabilityTest(void)
{
    printf("Starting Flash stability test...\r\n");
    
    for(int i = 0; i < 10; i++) {
        printf("Test iteration %d\r\n", i);
        
        // 写入测试数据
        FlashStorage_Status status = FlashStorage_StoreData1(i);
        if(status != FLASH_STORAGE_OK) {
            printf("Write failed at iteration %d\r\n", i);
            return status;
        }
        
        // 读取并验证
        int32_t read_value;
        status = FlashStorage_GetData1Value(&read_value);
        if(status != FLASH_STORAGE_OK) {
            printf("Read failed at iteration %d\r\n", i);
            return status;
        }
        
        if(read_value != i) {
            printf("Test failed at iteration %d: expected %d, got %d\r\n", 
                   i, i, read_value);
            return FLASH_STORAGE_ERROR;
        }
        
        printf("Iteration %d passed\r\n", i);
    }
    
    printf("Flash stability test completed successfully\r\n");
    return FLASH_STORAGE_OK;
}

/**
 * @brief 获取当前使用的Flash操作方法
 * @retval uint8_t 当前方法 (0=HAL库, 1=寄存器)
 */
uint8_t FlashStorage_GetCurrentMethod(void)
{
    return (uint8_t)current_method;
}