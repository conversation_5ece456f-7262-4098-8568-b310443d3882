#include "interrupt.h"

#define STATE_DELAY_MS 200  // 状态延迟时间200ms
#define WAIT_DELAY_MS 200  // 等待到位延迟时间200ms

struct state_machine State_Machine;

uint32_t state_start_time = 0;  // 状态开始时间戳
uint16_t task2_count;
uint8_t arrive_flag;
void task2_proc(void);
void task3_proc(void);
int16_t count1;
int16_t count2;
void State_Machine_init()
{
    State_Machine.MAIN_STATE = STATE_IDLE;
    State_Machine.STATE_TASK2 = task2_state0;
    State_Machine.STATE_TASK3 = task3_state0;
    State_Machine.STATE_TASK4 = STATE_IDLE;//没改
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	if(htim -> Instance == TIM6)//20ms进入一次中断
	{
		switch(State_Machine.MAIN_STATE)
		{
			case STATE_IDLE://IDLE
				
			break;
			case TASK_2://第二问逻辑
			{
				task2_proc();
			}
			break;
			case TASK_3: //第三问逻辑
			{
				task3_proc();
			}
			break;
			case TASK_4: //第四问逻辑
				
			break;
			default:
				
			break;
		}	
	}	
}

void task3_proc()
{
	switch(State_Machine.STATE_TASK3)
	{
		case task3_state0:
			Relay(0);
			track_control();
			if(arrive_flag) State_Machine.STATE_TASK3 = task3_state1;
		break;
		case task3_state1:
			Relay(1);
			State_Machine.STATE_TASK3 = task3_state0;
			State_Machine.MAIN_STATE = STATE_IDLE;
		break;
	}
}
void task2_proc()
{	
	switch(State_Machine.STATE_TASK2)
	{
		case task2_state0: //立即定位
			Relay(0);
			motorB_pos_control(num2,200);
			motorA_pos_control(num1,200);
			State_Machine.STATE_TASK2 = task2_state1;
		break;
		case task2_state1: //等待1s的关闭激光
			if(++task2_count == 50)
			{
				task2_count = 0;
				State_Machine.STATE_TASK2 = task2_state2;
			}
		break;
		case task2_state2: //开启激光
			Relay(1);
			State_Machine.MAIN_STATE = STATE_IDLE;
			State_Machine.STATE_TASK2 = task2_state0;	
//			if(task2_count == 0) //RelayON
//			if(++task2_count == 25)
//			{
//				task2_count = 0;
//				State_Machine.STATE_TASK2 = task2_state3;
//			}		
		break;
//		case task2_state3: //关闭激光
//			//Relay
//			if(task2_count == 0) //RelayON
//			if(++task2_count == 25)
//			{
//				task2_count = 0;
//				State_Machine.MAIN_STATE = STATE_IDLE;
//				State_Machine.STATE_TASK2 = task2_state0;
//			}		
//		break;
	}
}
