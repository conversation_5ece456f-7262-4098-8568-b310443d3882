#ifndef USART_APP_H
#define USART_APP_H

#include "mydefine.h"
// 外部变量声明
extern uint64_t uart_rx_ticks;
extern unsigned char uart_rx_index;
extern unsigned char uart_rx_buffer[128];
extern unsigned char uart_flag;
extern int32_t pos_y;
extern int32_t pos_x;
extern int32_t ff_pos_x_array[4];  // X位置数组声明
extern int32_t ff_pos_y_array[4];  // Y位置数组声明
extern uint32_t current_X;
extern uint32_t current_X;
extern uint8_t step_y_flag;
extern uint8_t step_x_flag;
extern int16_t camera_x_error;  // 摄像头X轴误差值（带符号，范围-255到255）
extern int16_t camera_y_error;  // 摄像头Y轴误差值（带符号，范围-255到255）
// 函数声明
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
void uart_task(void);
void uart_process(void);
void uart_init(void);
void handle_cmd_00(void);   // 处理命令00 - 清除FF数组
void jiaozhun_01(void);   // 处理命令01
void jiaozhun_02(void);   // 处理命令02
void jiaozhun_03(void);   // 处理命令03
void jiaozhun_04(void);   // 处理命令04
void handle_cmd_ff(void);   // 处理命令FF
void task_01(void);   // 处理88命令01
void task_02(void);   // 处理88命令02
void task_03(void);   // 处理88命令03
void task_04(void);   // 处理88命令04


#endif
