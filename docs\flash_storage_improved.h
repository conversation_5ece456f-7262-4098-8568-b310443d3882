/**
 * @file flash_storage_improved.h
 * @brief 改进的STM32F407VET6 Flash存储管理头文件
 * <AUTHOR> (米醋电子工作室) - 基于网络调研优化
 * @version 2.0
 * @date 2025-01-30
 * @note 解决HAL库兼容性问题，增加寄存器直接操作支持
 */

#ifndef __FLASH_STORAGE_IMPROVED_H
#define __FLASH_STORAGE_IMPROVED_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "mydefine.h"

/* Exported types ------------------------------------------------------------*/
/**
 * @brief Flash操作状态枚举 - 扩展版本
 */
typedef enum {
    FLASH_STORAGE_OK = 0,                    // 操作成功
    FLASH_STORAGE_ERROR,                     // 操作失败
    FLASH_STORAGE_TIMEOUT,                   // 操作超时
    FLASH_STORAGE_INVALID_PARAM,             // 参数无效
    FLASH_STORAGE_WRITE_PROTECTED,           // 写保护错误
    FLASH_STORAGE_ALIGNMENT_ERROR,           // 对齐错误
    FLASH_STORAGE_PARALLELISM_ERROR,         // 并行度错误
    FLASH_STORAGE_SEQUENCE_ERROR,            // 序列错误
    FLASH_STORAGE_OPERATION_ERROR,           // 操作错误
    FLASH_STORAGE_ERASE_VERIFY_FAILED,       // 擦除验证失败
    FLASH_STORAGE_WRITE_VERIFY_FAILED        // 写入验证失败
} FlashStorage_Status;

/**
 * @brief 数据结构体 - 扩展版本
 */
typedef struct {
    uint8_t sign;       // 符号位 (0=正数, 1=负数)
    uint32_t size;      // 数据大小
    uint32_t checksum;  // 校验和 (可选)
} DataInfo_Enhanced_t;

/**
 * @brief Flash操作方法枚举
 */
typedef enum {
    FLASH_METHOD_HAL = 0,      // 使用HAL库
    FLASH_METHOD_REGISTER      // 使用寄存器直接操作
} FlashMethod_t;

/* Exported constants --------------------------------------------------------*/
// STM32F407VET6 Flash扇区定义
#define FLASH_SECTOR_6_BASE         0x08040000UL    // Sector 6起始地址 (128KB)
#define FLASH_SECTOR_7_BASE         0x08060000UL    // Sector 7起始地址 (128KB)

// 数据1存储地址 (使用Sector 6的最后8字节)
#define DATA1_SIGN_ADDR             0x0805FFF8UL    // 第一个数据符号位地址
#define DATA1_SIZE_ADDR             0x0805FFFCUL    // 第一个数据大小地址

// 数据2存储地址 (使用Sector 7的最后8字节)
#define DATA2_SIGN_ADDR             0x0807FFF8UL    // 第二个数据符号位地址
#define DATA2_SIZE_ADDR             0x0807FFFCUL    // 第二个数据大小地址

// Flash操作相关常量
#define FLASH_TIMEOUT_VALUE         50000UL         // Flash操作超时值
#define FLASH_VOLTAGE_RANGE         FLASH_VOLTAGE_RANGE_3  // 电压范围 2.7V-3.6V

// Flash扇区号定义
#define FLASH_SECTOR_6_NUMBER       6
#define FLASH_SECTOR_7_NUMBER       7

/* Exported macro ------------------------------------------------------------*/
#define IS_VALID_SIGN(sign)         ((sign) <= 1)
#define IS_VALID_SIZE(size)         ((size) > 0)
#define IS_VALID_METHOD(method)     ((method) <= 1)

/* Exported functions prototypes ---------------------------------------------*/

/**
 * @brief 初始化Flash存储模块
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_Init(void);

/**
 * @brief 设置Flash操作方法
 * @param method: 操作方法 (0=HAL库, 1=寄存器)
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_SetMethod(uint8_t method);

/**
 * @brief 获取当前使用的Flash操作方法
 * @retval uint8_t 当前方法 (0=HAL库, 1=寄存器)
 */
uint8_t FlashStorage_GetCurrentMethod(void);

/**
 * @brief 改进的写入第一个数据的信息
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData1_Improved(uint8_t sign, uint32_t size);

/**
 * @brief 改进的写入第二个数据的信息
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData2_Improved(uint8_t sign, uint32_t size);

/**
 * @brief Flash稳定性测试
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_StabilityTest(void);

/**
 * @brief 诊断Flash状态
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_DiagnoseStatus(void);

/**
 * @brief 验证扇区擦除效果
 * @param sector_base: 扇区基地址
 * @param size: 检查大小
 * @retval bool true=擦除成功, false=擦除失败
 */
bool FlashStorage_VerifyErase(uint32_t sector_base, uint32_t size);

/**
 * @brief 验证数据写入效果
 * @param address: 地址
 * @param expected_data: 期望数据
 * @retval bool true=写入成功, false=写入失败
 */
bool FlashStorage_VerifyWrite(uint32_t address, uint32_t expected_data);

// 保持原有接口的兼容性
/**
 * @brief 写入第一个数据的信息 (兼容性接口)
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData1(uint8_t sign, uint32_t size);

/**
 * @brief 写入第二个数据的信息 (兼容性接口)
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData2(uint8_t sign, uint32_t size);

/**
 * @brief 读取第一个数据的信息
 * @param data_info: 指向数据信息结构体的指针
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_ReadData1(DataInfo_t *data_info);

/**
 * @brief 读取第二个数据的信息
 * @param data_info: 指向数据信息结构体的指针
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_ReadData2(DataInfo_t *data_info);

/**
 * @brief 检查存储区域是否为空
 * @retval bool true=空, false=非空
 */
bool FlashStorage_IsEmpty(void);

/**
 * @brief 获取Flash存储区域状态信息
 * @param data1_info: 第一个数据信息
 * @param data2_info: 第二个数据信息
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_GetStatus(DataInfo_t *data1_info, DataInfo_t *data2_info);

/**
 * @brief 直接读取第一个数据的原始值（带符号）
 * @param value: 指向存储结果的指针
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_GetData1Value(int32_t *value);

/**
 * @brief 直接读取第二个数据的原始值（带符号）
 * @param value: 指向存储结果的指针
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_GetData2Value(int32_t *value);

/**
 * @brief 直接存储第一个数据（自动处理符号位）
 * @param value: 要存储的数据值（可以是正数或负数）
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_StoreData1(int32_t value);

/**
 * @brief 直接存储第二个数据（自动处理符号位）
 * @param value: 要存储的数据值（可以是正数或负数）
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_StoreData2(int32_t value);

#ifdef __cplusplus
}
#endif

#endif /* __FLASH_STORAGE_IMPROVED_H */